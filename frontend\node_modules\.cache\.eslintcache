[{"D:\\xyz\\vectorshift\\frontend\\src\\index.js": "1", "D:\\xyz\\vectorshift\\frontend\\src\\App.js": "2", "D:\\xyz\\vectorshift\\frontend\\src\\submit.js": "3", "D:\\xyz\\vectorshift\\frontend\\src\\toolbar.js": "4", "D:\\xyz\\vectorshift\\frontend\\src\\ui.js": "5", "D:\\xyz\\vectorshift\\frontend\\src\\draggableNode.js": "6", "D:\\xyz\\vectorshift\\frontend\\src\\store.js": "7", "D:\\xyz\\vectorshift\\frontend\\src\\nodes\\textNode.js": "8", "D:\\xyz\\vectorshift\\frontend\\src\\nodes\\inputNode.js": "9", "D:\\xyz\\vectorshift\\frontend\\src\\nodes\\llmNode.js": "10", "D:\\xyz\\vectorshift\\frontend\\src\\nodes\\outputNode.js": "11"}, {"size": 254, "mtime": 1751513940304, "results": "12", "hashOfConfig": "13"}, {"size": 269, "mtime": 1751513940335, "results": "14", "hashOfConfig": "13"}, {"size": 227, "mtime": 1751518020078, "results": "15", "hashOfConfig": "13"}, {"size": 552, "mtime": 1751513940330, "results": "16", "hashOfConfig": "13"}, {"size": 3362, "mtime": 1751513940299, "results": "17", "hashOfConfig": "13"}, {"size": 942, "mtime": 1751513940315, "results": "18", "hashOfConfig": "13"}, {"size": 1336, "mtime": 1751513940309, "results": "19", "hashOfConfig": "13"}, {"size": 746, "mtime": 1751553787120, "results": "20", "hashOfConfig": "13"}, {"size": 1154, "mtime": 1751553787122, "results": "21", "hashOfConfig": "13"}, {"size": 707, "mtime": 1751553786951, "results": "22", "hashOfConfig": "13"}, {"size": 1165, "mtime": 1751553787104, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "27"}, "1xb0qsq", {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "27"}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "27"}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "40", "usedDeprecatedRules": "27"}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "27"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "27"}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\xyz\\vectorshift\\frontend\\src\\index.js", [], [], [], "D:\\xyz\\vectorshift\\frontend\\src\\App.js", [], [], "D:\\xyz\\vectorshift\\frontend\\src\\submit.js", [], [], "D:\\xyz\\vectorshift\\frontend\\src\\toolbar.js", [], [], "D:\\xyz\\vectorshift\\frontend\\src\\ui.js", ["59"], [], "// ui.js\n// Displays the drag-and-drop UI\n// --------------------------------------------------\n\nimport { useState, useRef, useCallback } from 'react';\nimport ReactFlow, { Controls, Background, MiniMap } from 'reactflow';\nimport { useStore } from './store';\nimport { shallow } from 'zustand/shallow';\nimport { InputNode } from './nodes/inputNode';\nimport { LLMNode } from './nodes/llmNode';\nimport { OutputNode } from './nodes/outputNode';\nimport { TextNode } from './nodes/textNode';\n\nimport 'reactflow/dist/style.css';\n\nconst gridSize = 20;\nconst proOptions = { hideAttribution: true };\nconst nodeTypes = {\n  customInput: InputNode,\n  llm: LLMNode,\n  customOutput: OutputNode,\n  text: TextNode,\n};\n\nconst selector = (state) => ({\n  nodes: state.nodes,\n  edges: state.edges,\n  getNodeID: state.getNodeID,\n  addNode: state.addNode,\n  onNodesChange: state.onNodesChange,\n  onEdgesChange: state.onEdgesChange,\n  onConnect: state.onConnect,\n});\n\nexport const PipelineUI = () => {\n    const reactFlowWrapper = useRef(null);\n    const [reactFlowInstance, setReactFlowInstance] = useState(null);\n    const {\n      nodes,\n      edges,\n      getNodeID,\n      addNode,\n      onNodesChange,\n      onEdgesChange,\n      onConnect\n    } = useStore(selector, shallow);\n\n    const getInitNodeData = (nodeID, type) => {\n      let nodeData = { id: nodeID, nodeType: `${type}` };\n      return nodeData;\n    }\n\n    const onDrop = useCallback(\n        (event) => {\n          event.preventDefault();\n    \n          const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();\n          if (event?.dataTransfer?.getData('application/reactflow')) {\n            const appData = JSON.parse(event.dataTransfer.getData('application/reactflow'));\n            const type = appData?.nodeType;\n      \n            // check if the dropped element is valid\n            if (typeof type === 'undefined' || !type) {\n              return;\n            }\n      \n            const position = reactFlowInstance.project({\n              x: event.clientX - reactFlowBounds.left,\n              y: event.clientY - reactFlowBounds.top,\n            });\n\n            const nodeID = getNodeID(type);\n            const newNode = {\n              id: nodeID,\n              type,\n              position,\n              data: getInitNodeData(nodeID, type),\n            };\n      \n            addNode(newNode);\n          }\n        },\n        [reactFlowInstance]\n    );\n\n    const onDragOver = useCallback((event) => {\n        event.preventDefault();\n        event.dataTransfer.dropEffect = 'move';\n    }, []);\n\n    return (\n        <>\n        <div ref={reactFlowWrapper} style={{width: '100wv', height: '70vh'}}>\n            <ReactFlow\n                nodes={nodes}\n                edges={edges}\n                onNodesChange={onNodesChange}\n                onEdgesChange={onEdgesChange}\n                onConnect={onConnect}\n                onDrop={onDrop}\n                onDragOver={onDragOver}\n                onInit={setReactFlowInstance}\n                nodeTypes={nodeTypes}\n                proOptions={proOptions}\n                snapGrid={[gridSize, gridSize]}\n                connectionLineType='smoothstep'\n            >\n                <Background color=\"#aaa\" gap={gridSize} />\n                <Controls />\n                <MiniMap />\n            </ReactFlow>\n        </div>\n        </>\n    )\n}\n", "D:\\xyz\\vectorshift\\frontend\\src\\draggableNode.js", [], [], "D:\\xyz\\vectorshift\\frontend\\src\\store.js", [], [], "D:\\xyz\\vectorshift\\frontend\\src\\nodes\\textNode.js", [], [], "D:\\xyz\\vectorshift\\frontend\\src\\nodes\\inputNode.js", [], [], "D:\\xyz\\vectorshift\\frontend\\src\\nodes\\llmNode.js", [], [], "D:\\xyz\\vectorshift\\frontend\\src\\nodes\\outputNode.js", [], [], {"ruleId": "60", "severity": 1, "message": "61", "line": 83, "column": 9, "nodeType": "62", "endLine": 83, "endColumn": 28, "suggestions": "63"}, "react-hooks/exhaustive-deps", "React Hook useCallback has missing dependencies: 'addNode' and 'getNodeID'. Either include them or remove the dependency array.", "ArrayExpression", ["64"], {"desc": "65", "fix": "66"}, "Update the dependencies array to be: [addNode, getNodeID, reactFlowInstance]", {"range": "67", "text": "68"}, [2396, 2415], "[addNode, getNodeID, reactFlowInstance]"]