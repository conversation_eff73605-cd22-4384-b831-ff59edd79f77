{"ast": null, "code": "var _jsxFileName = \"D:\\\\xyz\\\\vectorshift\\\\frontend\\\\src\\\\nodes\\\\outputNode.js\";\n// outputNode.js\n\nimport { Position } from 'reactflow';\nimport { BaseNode } from './BaseNode';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const OutputNode = ({\n  id,\n  data\n}) => {\n  const handles = [{\n    type: \"target\",\n    position: Position.Left,\n    id: `${id}-value`\n  }];\n  const fields = [{\n    type: 'input',\n    label: 'Name',\n    stateKey: 'outputName',\n    defaultValue: id.replace('customOutput-', 'output_')\n  }, {\n    type: 'select',\n    label: 'Type',\n    stateKey: 'outputType',\n    defaultValue: 'Text',\n    options: [{\n      value: 'Text',\n      label: 'Text'\n    }, {\n      value: 'File',\n      label: 'Image'\n    }]\n  }];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Output\",\n    handles: handles,\n    fields: fields\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_c = OutputNode;\nvar _c;\n$RefreshReg$(_c, \"OutputNode\");", "map": {"version": 3, "names": ["Position", "BaseNode", "jsxDEV", "_jsxDEV", "OutputNode", "id", "data", "handles", "type", "position", "Left", "fields", "label", "stateKey", "defaultValue", "replace", "options", "value", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/xyz/vectorshift/frontend/src/nodes/outputNode.js"], "sourcesContent": ["// outputNode.js\n\nimport { Position } from 'reactflow';\nimport { BaseNode } from './BaseNode';\n\nexport const OutputNode = ({ id, data }) => {\n  const handles = [\n    {\n      type: \"target\",\n      position: Position.Left,\n      id: `${id}-value`\n    }\n  ];\n\n  const fields = [\n    {\n      type: 'input',\n      label: 'Name',\n      stateKey: 'outputName',\n      defaultValue: id.replace('customOutput-', 'output_')\n    },\n    {\n      type: 'select',\n      label: 'Type',\n      stateKey: 'outputType',\n      defaultValue: 'Text',\n      options: [\n        { value: 'Text', label: 'Text' },\n        { value: 'File', label: 'Image' }\n      ]\n    }\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Output\"\n      handles={handles}\n      fields={fields}\n    />\n  );\n};\n"], "mappings": ";AAAA;;AAEA,SAASA,QAAQ,QAAQ,WAAW;AACpC,SAASC,QAAQ,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,OAAO,MAAMC,UAAU,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAC1C,MAAMC,OAAO,GAAG,CACd;IACEC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAET,QAAQ,CAACU,IAAI;IACvBL,EAAE,EAAG,GAAEA,EAAG;EACZ,CAAC,CACF;EAED,MAAMM,MAAM,GAAG,CACb;IACEH,IAAI,EAAE,OAAO;IACbI,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,YAAY;IACtBC,YAAY,EAAET,EAAE,CAACU,OAAO,CAAC,eAAe,EAAE,SAAS;EACrD,CAAC,EACD;IACEP,IAAI,EAAE,QAAQ;IACdI,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,YAAY;IACtBC,YAAY,EAAE,MAAM;IACpBE,OAAO,EAAE,CACP;MAAEC,KAAK,EAAE,MAAM;MAAEL,KAAK,EAAE;IAAO,CAAC,EAChC;MAAEK,KAAK,EAAE,MAAM;MAAEL,KAAK,EAAE;IAAQ,CAAC;EAErC,CAAC,CACF;EAED,oBACET,OAAA,CAACF,QAAQ;IACPI,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXY,KAAK,EAAC,QAAQ;IACdX,OAAO,EAAEA,OAAQ;IACjBI,MAAM,EAAEA;EAAO;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChB,CAAC;AAEN,CAAC;AAACC,EAAA,GArCWnB,UAAU;AAAA,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}