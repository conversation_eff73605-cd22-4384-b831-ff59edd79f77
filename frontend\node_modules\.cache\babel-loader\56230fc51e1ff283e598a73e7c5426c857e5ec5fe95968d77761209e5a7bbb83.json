{"ast": null, "code": "var _jsxFileName = \"D:\\\\xyz\\\\vectorshift\\\\frontend\\\\src\\\\nodes\\\\BaseNode.js\",\n  _s = $RefreshSig$();\n// BaseNode.js\n\nimport { useState } from 'react';\nimport { Handle, Position } from 'reactflow';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const BaseNode = ({\n  id,\n  data,\n  title,\n  description,\n  handles = [],\n  fields = [],\n  style = {},\n  className = \"\"\n}) => {\n  _s();\n  // Dynamic state management based on fields configuration\n  const [state, setState] = useState(() => {\n    const initialState = {};\n    fields.forEach(field => {\n      if (field.stateKey) {\n        initialState[field.stateKey] = (data === null || data === void 0 ? void 0 : data[field.stateKey]) || field.defaultValue || '';\n      }\n    });\n    return initialState;\n  });\n\n  // Generic handler for state updates\n  const handleStateChange = (stateKey, value) => {\n    setState(prev => ({\n      ...prev,\n      [stateKey]: value\n    }));\n  };\n\n  // Default styling\n  const defaultStyle = {\n    width: 200,\n    height: 80,\n    border: '1px solid black',\n    padding: '8px',\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    ...style\n  };\n\n  // Render form fields based on configuration\n  const renderFields = () => {\n    return fields.map((field, index) => {\n      switch (field.type) {\n        case 'input':\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '4px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              children: [field.label, \":\", /*#__PURE__*/_jsxDEV(\"input\", {\n                type: field.inputType || 'text',\n                value: state[field.stateKey] || '',\n                onChange: e => handleStateChange(field.stateKey, e.target.value),\n                style: {\n                  marginLeft: '4px',\n                  fontSize: '12px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this);\n        case 'select':\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '4px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              children: [field.label, \":\", /*#__PURE__*/_jsxDEV(\"select\", {\n                value: state[field.stateKey] || '',\n                onChange: e => handleStateChange(field.stateKey, e.target.value),\n                style: {\n                  marginLeft: '4px',\n                  fontSize: '12px'\n                },\n                children: field.options.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: option.value,\n                  children: option.label\n                }, option.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 76,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this);\n        case 'textarea':\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '4px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              children: [field.label, \":\", /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: state[field.stateKey] || '',\n                onChange: e => handleStateChange(field.stateKey, e.target.value),\n                rows: field.rows || 2,\n                style: {\n                  marginLeft: '4px',\n                  fontSize: '12px',\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this);\n        case 'text':\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '4px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '12px'\n              },\n              children: field.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this);\n        default:\n          return null;\n      }\n    });\n  };\n\n  // Render handles based on configuration\n  const renderHandles = () => {\n    return handles.map((handle, index) => /*#__PURE__*/_jsxDEV(Handle, {\n      type: handle.type,\n      position: handle.position,\n      id: handle.id,\n      style: handle.style || {}\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: defaultStyle,\n    className: className,\n    children: [renderHandles(), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '8px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          fontWeight: 'bold',\n          fontSize: '14px'\n        },\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), description && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '8px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          fontSize: '12px',\n          color: '#666'\n        },\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: renderFields()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 127,\n    columnNumber: 5\n  }, this);\n};\n_s(BaseNode, \"de/XYKhnQzYM/Kce9cKG0+eG5uQ=\");\n_c = BaseNode;\nvar _c;\n$RefreshReg$(_c, \"BaseNode\");", "map": {"version": 3, "names": ["useState", "<PERSON><PERSON>", "Position", "jsxDEV", "_jsxDEV", "BaseNode", "id", "data", "title", "description", "handles", "fields", "style", "className", "_s", "state", "setState", "initialState", "for<PERSON>ach", "field", "stateKey", "defaultValue", "handleStateChange", "value", "prev", "defaultStyle", "width", "height", "border", "padding", "backgroundColor", "borderRadius", "renderFields", "map", "index", "type", "marginBottom", "children", "label", "inputType", "onChange", "e", "target", "marginLeft", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "options", "option", "rows", "content", "<PERSON><PERSON><PERSON><PERSON>", "handle", "position", "fontWeight", "color", "_c", "$RefreshReg$"], "sources": ["D:/xyz/vectorshift/frontend/src/nodes/BaseNode.js"], "sourcesContent": ["// BaseNode.js\n\nimport { useState } from 'react';\nimport { Handle, Position } from 'reactflow';\n\nexport const BaseNode = ({ \n  id, \n  data, \n  title,\n  description,\n  handles = [],\n  fields = [],\n  style = {},\n  className = \"\"\n}) => {\n  // Dynamic state management based on fields configuration\n  const [state, setState] = useState(() => {\n    const initialState = {};\n    fields.forEach(field => {\n      if (field.stateKey) {\n        initialState[field.stateKey] = data?.[field.stateKey] || field.defaultValue || '';\n      }\n    });\n    return initialState;\n  });\n\n  // Generic handler for state updates\n  const handleStateChange = (stateKey, value) => {\n    setState(prev => ({\n      ...prev,\n      [stateKey]: value\n    }));\n  };\n\n  // Default styling\n  const defaultStyle = {\n    width: 200,\n    height: 80,\n    border: '1px solid black',\n    padding: '8px',\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    ...style\n  };\n\n  // Render form fields based on configuration\n  const renderFields = () => {\n    return fields.map((field, index) => {\n      switch (field.type) {\n        case 'input':\n          return (\n            <div key={index} style={{ marginBottom: '4px' }}>\n              <label>\n                {field.label}:\n                <input\n                  type={field.inputType || 'text'}\n                  value={state[field.stateKey] || ''}\n                  onChange={(e) => handleStateChange(field.stateKey, e.target.value)}\n                  style={{ marginLeft: '4px', fontSize: '12px' }}\n                />\n              </label>\n            </div>\n          );\n        \n        case 'select':\n          return (\n            <div key={index} style={{ marginBottom: '4px' }}>\n              <label>\n                {field.label}:\n                <select\n                  value={state[field.stateKey] || ''}\n                  onChange={(e) => handleStateChange(field.stateKey, e.target.value)}\n                  style={{ marginLeft: '4px', fontSize: '12px' }}\n                >\n                  {field.options.map(option => (\n                    <option key={option.value} value={option.value}>\n                      {option.label}\n                    </option>\n                  ))}\n                </select>\n              </label>\n            </div>\n          );\n        \n        case 'textarea':\n          return (\n            <div key={index} style={{ marginBottom: '4px' }}>\n              <label>\n                {field.label}:\n                <textarea\n                  value={state[field.stateKey] || ''}\n                  onChange={(e) => handleStateChange(field.stateKey, e.target.value)}\n                  rows={field.rows || 2}\n                  style={{ marginLeft: '4px', fontSize: '12px', width: '100%' }}\n                />\n              </label>\n            </div>\n          );\n        \n        case 'text':\n          return (\n            <div key={index} style={{ marginBottom: '4px' }}>\n              <span style={{ fontSize: '12px' }}>{field.content}</span>\n            </div>\n          );\n        \n        default:\n          return null;\n      }\n    });\n  };\n\n  // Render handles based on configuration\n  const renderHandles = () => {\n    return handles.map((handle, index) => (\n      <Handle\n        key={index}\n        type={handle.type}\n        position={handle.position}\n        id={handle.id}\n        style={handle.style || {}}\n      />\n    ));\n  };\n\n  return (\n    <div style={defaultStyle} className={className}>\n      {renderHandles()}\n      \n      <div style={{ marginBottom: '8px' }}>\n        <span style={{ fontWeight: 'bold', fontSize: '14px' }}>{title}</span>\n      </div>\n      \n      {description && (\n        <div style={{ marginBottom: '8px' }}>\n          <span style={{ fontSize: '12px', color: '#666' }}>{description}</span>\n        </div>\n      )}\n      \n      <div>\n        {renderFields()}\n      </div>\n    </div>\n  );\n};\n"], "mappings": ";;AAAA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,MAAM,EAAEC,QAAQ,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,OAAO,MAAMC,QAAQ,GAAGA,CAAC;EACvBC,EAAE;EACFC,IAAI;EACJC,KAAK;EACLC,WAAW;EACXC,OAAO,GAAG,EAAE;EACZC,MAAM,GAAG,EAAE;EACXC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ;EACA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,MAAM;IACvC,MAAMiB,YAAY,GAAG,CAAC,CAAC;IACvBN,MAAM,CAACO,OAAO,CAACC,KAAK,IAAI;MACtB,IAAIA,KAAK,CAACC,QAAQ,EAAE;QAClBH,YAAY,CAACE,KAAK,CAACC,QAAQ,CAAC,GAAG,CAAAb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAGY,KAAK,CAACC,QAAQ,CAAC,KAAID,KAAK,CAACE,YAAY,IAAI,EAAE;MACnF;IACF,CAAC,CAAC;IACF,OAAOJ,YAAY;EACrB,CAAC,CAAC;;EAEF;EACA,MAAMK,iBAAiB,GAAGA,CAACF,QAAQ,EAAEG,KAAK,KAAK;IAC7CP,QAAQ,CAACQ,IAAI,KAAK;MAChB,GAAGA,IAAI;MACP,CAACJ,QAAQ,GAAGG;IACd,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,YAAY,GAAG;IACnBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,iBAAiB;IACzBC,OAAO,EAAE,KAAK;IACdC,eAAe,EAAE,OAAO;IACxBC,YAAY,EAAE,KAAK;IACnB,GAAGnB;EACL,CAAC;;EAED;EACA,MAAMoB,YAAY,GAAGA,CAAA,KAAM;IACzB,OAAOrB,MAAM,CAACsB,GAAG,CAAC,CAACd,KAAK,EAAEe,KAAK,KAAK;MAClC,QAAQf,KAAK,CAACgB,IAAI;QAChB,KAAK,OAAO;UACV,oBACE/B,OAAA;YAAiBQ,KAAK,EAAE;cAAEwB,YAAY,EAAE;YAAM,CAAE;YAAAC,QAAA,eAC9CjC,OAAA;cAAAiC,QAAA,GACGlB,KAAK,CAACmB,KAAK,EAAC,GACb,eAAAlC,OAAA;gBACE+B,IAAI,EAAEhB,KAAK,CAACoB,SAAS,IAAI,MAAO;gBAChChB,KAAK,EAAER,KAAK,CAACI,KAAK,CAACC,QAAQ,CAAC,IAAI,EAAG;gBACnCoB,QAAQ,EAAGC,CAAC,IAAKnB,iBAAiB,CAACH,KAAK,CAACC,QAAQ,EAAEqB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;gBACnEX,KAAK,EAAE;kBAAE+B,UAAU,EAAE,KAAK;kBAAEC,QAAQ,EAAE;gBAAO;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC,GATAd,KAAK;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUV,CAAC;QAGV,KAAK,QAAQ;UACX,oBACE5C,OAAA;YAAiBQ,KAAK,EAAE;cAAEwB,YAAY,EAAE;YAAM,CAAE;YAAAC,QAAA,eAC9CjC,OAAA;cAAAiC,QAAA,GACGlB,KAAK,CAACmB,KAAK,EAAC,GACb,eAAAlC,OAAA;gBACEmB,KAAK,EAAER,KAAK,CAACI,KAAK,CAACC,QAAQ,CAAC,IAAI,EAAG;gBACnCoB,QAAQ,EAAGC,CAAC,IAAKnB,iBAAiB,CAACH,KAAK,CAACC,QAAQ,EAAEqB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;gBACnEX,KAAK,EAAE;kBAAE+B,UAAU,EAAE,KAAK;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAP,QAAA,EAE9ClB,KAAK,CAAC8B,OAAO,CAAChB,GAAG,CAACiB,MAAM,iBACvB9C,OAAA;kBAA2BmB,KAAK,EAAE2B,MAAM,CAAC3B,KAAM;kBAAAc,QAAA,EAC5Ca,MAAM,CAACZ;gBAAK,GADFY,MAAM,CAAC3B,KAAK;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC,GAdAd,KAAK;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeV,CAAC;QAGV,KAAK,UAAU;UACb,oBACE5C,OAAA;YAAiBQ,KAAK,EAAE;cAAEwB,YAAY,EAAE;YAAM,CAAE;YAAAC,QAAA,eAC9CjC,OAAA;cAAAiC,QAAA,GACGlB,KAAK,CAACmB,KAAK,EAAC,GACb,eAAAlC,OAAA;gBACEmB,KAAK,EAAER,KAAK,CAACI,KAAK,CAACC,QAAQ,CAAC,IAAI,EAAG;gBACnCoB,QAAQ,EAAGC,CAAC,IAAKnB,iBAAiB,CAACH,KAAK,CAACC,QAAQ,EAAEqB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;gBACnE4B,IAAI,EAAEhC,KAAK,CAACgC,IAAI,IAAI,CAAE;gBACtBvC,KAAK,EAAE;kBAAE+B,UAAU,EAAE,KAAK;kBAAEC,QAAQ,EAAE,MAAM;kBAAElB,KAAK,EAAE;gBAAO;cAAE;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC,GATAd,KAAK;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUV,CAAC;QAGV,KAAK,MAAM;UACT,oBACE5C,OAAA;YAAiBQ,KAAK,EAAE;cAAEwB,YAAY,EAAE;YAAM,CAAE;YAAAC,QAAA,eAC9CjC,OAAA;cAAMQ,KAAK,EAAE;gBAAEgC,QAAQ,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAElB,KAAK,CAACiC;YAAO;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC,GADjDd,KAAK;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CAAC;QAGV;UACE,OAAO,IAAI;MACf;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMK,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAO3C,OAAO,CAACuB,GAAG,CAAC,CAACqB,MAAM,EAAEpB,KAAK,kBAC/B9B,OAAA,CAACH,MAAM;MAELkC,IAAI,EAAEmB,MAAM,CAACnB,IAAK;MAClBoB,QAAQ,EAAED,MAAM,CAACC,QAAS;MAC1BjD,EAAE,EAAEgD,MAAM,CAAChD,EAAG;MACdM,KAAK,EAAE0C,MAAM,CAAC1C,KAAK,IAAI,CAAC;IAAE,GAJrBsB,KAAK;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKX,CACF,CAAC;EACJ,CAAC;EAED,oBACE5C,OAAA;IAAKQ,KAAK,EAAEa,YAAa;IAACZ,SAAS,EAAEA,SAAU;IAAAwB,QAAA,GAC5CgB,aAAa,CAAC,CAAC,eAEhBjD,OAAA;MAAKQ,KAAK,EAAE;QAAEwB,YAAY,EAAE;MAAM,CAAE;MAAAC,QAAA,eAClCjC,OAAA;QAAMQ,KAAK,EAAE;UAAE4C,UAAU,EAAE,MAAM;UAAEZ,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,EAAE7B;MAAK;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClE,CAAC,EAELvC,WAAW,iBACVL,OAAA;MAAKQ,KAAK,EAAE;QAAEwB,YAAY,EAAE;MAAM,CAAE;MAAAC,QAAA,eAClCjC,OAAA;QAAMQ,KAAK,EAAE;UAAEgC,QAAQ,EAAE,MAAM;UAAEa,KAAK,EAAE;QAAO,CAAE;QAAApB,QAAA,EAAE5B;MAAW;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CACN,eAED5C,OAAA;MAAAiC,QAAA,EACGL,YAAY,CAAC;IAAC;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClC,EAAA,CA3IWT,QAAQ;AAAAqD,EAAA,GAARrD,QAAQ;AAAA,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}