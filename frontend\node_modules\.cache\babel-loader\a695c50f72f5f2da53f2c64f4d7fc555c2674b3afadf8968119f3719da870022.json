{"ast": null, "code": "var _jsxFileName = \"D:\\\\xyz\\\\vectorshift\\\\frontend\\\\src\\\\nodes\\\\textNode.js\";\n// textNode.js\n\nimport { Position } from 'reactflow';\nimport { BaseNode } from './BaseNode';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const TextNode = ({\n  id,\n  data\n}) => {\n  const handles = [{\n    type: \"source\",\n    position: Position.Right,\n    id: `${id}-output`\n  }];\n  const fields = [{\n    type: 'input',\n    label: 'Text',\n    stateKey: 'text',\n    defaultValue: '{{input}}'\n  }];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Text\",\n    handles: handles,\n    fields: fields\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_c = TextNode;\nvar _c;\n$RefreshReg$(_c, \"TextNode\");", "map": {"version": 3, "names": ["Position", "BaseNode", "jsxDEV", "_jsxDEV", "TextNode", "id", "data", "handles", "type", "position", "Right", "fields", "label", "stateKey", "defaultValue", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/xyz/vectorshift/frontend/src/nodes/textNode.js"], "sourcesContent": ["// textNode.js\n\nimport { Position } from 'reactflow';\nimport { BaseNode } from './BaseNode';\n\nexport const TextNode = ({ id, data }) => {\n  const handles = [\n    {\n      type: \"source\",\n      position: Position.Right,\n      id: `${id}-output`\n    }\n  ];\n\n  const fields = [\n    {\n      type: 'input',\n      label: 'Text',\n      stateKey: 'text',\n      defaultValue: '{{input}}'\n    }\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Text\"\n      handles={handles}\n      fields={fields}\n    />\n  );\n};\n"], "mappings": ";AAAA;;AAEA,SAASA,QAAQ,QAAQ,WAAW;AACpC,SAASC,QAAQ,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,OAAO,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EACxC,MAAMC,OAAO,GAAG,CACd;IACEC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAET,QAAQ,CAACU,KAAK;IACxBL,EAAE,EAAG,GAAEA,EAAG;EACZ,CAAC,CACF;EAED,MAAMM,MAAM,GAAG,CACb;IACEH,IAAI,EAAE,OAAO;IACbI,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,MAAM;IAChBC,YAAY,EAAE;EAChB,CAAC,CACF;EAED,oBACEX,OAAA,CAACF,QAAQ;IACPI,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXS,KAAK,EAAC,MAAM;IACZR,OAAO,EAAEA,OAAQ;IACjBI,MAAM,EAAEA;EAAO;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChB,CAAC;AAEN,CAAC;AAACC,EAAA,GA3BWhB,QAAQ;AAAA,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}