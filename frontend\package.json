{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@craco/craco": "^7.1.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "reactflow": "^11.8.3", "web-vitals": "^2.1.4"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^9.8.8", "postcss": "^7.0.39", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17"}}