{"ast": null, "code": "var _jsxFileName = \"D:\\\\xyz\\\\vectorshift\\\\frontend\\\\src\\\\nodes\\\\inputNode.js\";\n// inputNode.js\n\nimport { Position } from 'reactflow';\nimport { BaseNode } from './BaseNode';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const InputNode = ({\n  id,\n  data\n}) => {\n  const handles = [{\n    type: \"source\",\n    position: Position.Right,\n    id: `${id}-value`\n  }];\n  const fields = [{\n    type: 'input',\n    label: 'Name',\n    stateKey: 'inputName',\n    defaultValue: id.replace('customInput-', 'input_')\n  }, {\n    type: 'select',\n    label: 'Type',\n    stateKey: 'inputType',\n    defaultValue: 'Text',\n    options: [{\n      value: 'Text',\n      label: 'Text'\n    }, {\n      value: 'File',\n      label: 'File'\n    }]\n  }];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Input\",\n    handles: handles,\n    fields: fields\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_c = InputNode;\nvar _c;\n$RefreshReg$(_c, \"InputNode\");", "map": {"version": 3, "names": ["Position", "BaseNode", "jsxDEV", "_jsxDEV", "InputNode", "id", "data", "handles", "type", "position", "Right", "fields", "label", "stateKey", "defaultValue", "replace", "options", "value", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/xyz/vectorshift/frontend/src/nodes/inputNode.js"], "sourcesContent": ["// inputNode.js\n\nimport { Position } from 'reactflow';\nimport { BaseNode } from './BaseNode';\n\nexport const InputNode = ({ id, data }) => {\n  const handles = [\n    {\n      type: \"source\",\n      position: Position.Right,\n      id: `${id}-value`\n    }\n  ];\n\n  const fields = [\n    {\n      type: 'input',\n      label: 'Name',\n      stateKey: 'inputName',\n      defaultValue: id.replace('customInput-', 'input_')\n    },\n    {\n      type: 'select',\n      label: 'Type',\n      stateKey: 'inputType',\n      defaultValue: 'Text',\n      options: [\n        { value: 'Text', label: 'Text' },\n        { value: 'File', label: 'File' }\n      ]\n    }\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Input\"\n      handles={handles}\n      fields={fields}\n    />\n  );\n};\n"], "mappings": ";AAAA;;AAEA,SAASA,QAAQ,QAAQ,WAAW;AACpC,SAASC,QAAQ,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,OAAO,MAAMC,SAAS,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EACzC,MAAMC,OAAO,GAAG,CACd;IACEC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAET,QAAQ,CAACU,KAAK;IACxBL,EAAE,EAAG,GAAEA,EAAG;EACZ,CAAC,CACF;EAED,MAAMM,MAAM,GAAG,CACb;IACEH,IAAI,EAAE,OAAO;IACbI,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,WAAW;IACrBC,YAAY,EAAET,EAAE,CAACU,OAAO,CAAC,cAAc,EAAE,QAAQ;EACnD,CAAC,EACD;IACEP,IAAI,EAAE,QAAQ;IACdI,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,WAAW;IACrBC,YAAY,EAAE,MAAM;IACpBE,OAAO,EAAE,CACP;MAAEC,KAAK,EAAE,MAAM;MAAEL,KAAK,EAAE;IAAO,CAAC,EAChC;MAAEK,KAAK,EAAE,MAAM;MAAEL,KAAK,EAAE;IAAO,CAAC;EAEpC,CAAC,CACF;EAED,oBACET,OAAA,CAACF,QAAQ;IACPI,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXY,KAAK,EAAC,OAAO;IACbX,OAAO,EAAEA,OAAQ;IACjBI,MAAM,EAAEA;EAAO;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChB,CAAC;AAEN,CAAC;AAACC,EAAA,GArCWnB,SAAS;AAAA,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}