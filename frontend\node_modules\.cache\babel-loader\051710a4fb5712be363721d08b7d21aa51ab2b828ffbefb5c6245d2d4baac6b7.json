{"ast": null, "code": "var _jsxFileName = \"D:\\\\xyz\\\\vectorshift\\\\frontend\\\\src\\\\nodes\\\\llmNode.js\";\n// llmNode.js\n\nimport { Position } from 'reactflow';\nimport { BaseNode } from './BaseNode';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const LLMNode = ({\n  id,\n  data\n}) => {\n  const handles = [{\n    type: \"target\",\n    position: Position.Left,\n    id: `${id}-system`,\n    style: {\n      top: `${100 / 3}%`\n    }\n  }, {\n    type: \"target\",\n    position: Position.Left,\n    id: `${id}-prompt`,\n    style: {\n      top: `${200 / 3}%`\n    }\n  }, {\n    type: \"source\",\n    position: Position.Right,\n    id: `${id}-response`\n  }];\n  const fields = [{\n    type: 'text',\n    content: 'This is a LLM.'\n  }];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"LLM\",\n    description: \"This is a LLM.\",\n    handles: handles,\n    fields: fields\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_c = LLMNode;\nvar _c;\n$RefreshReg$(_c, \"LLMNode\");", "map": {"version": 3, "names": ["Position", "BaseNode", "jsxDEV", "_jsxDEV", "LLMNode", "id", "data", "handles", "type", "position", "Left", "style", "top", "Right", "fields", "content", "title", "description", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/xyz/vectorshift/frontend/src/nodes/llmNode.js"], "sourcesContent": ["// llmNode.js\n\nimport { Position } from 'reactflow';\nimport { BaseNode } from './BaseNode';\n\nexport const LLMNode = ({ id, data }) => {\n  const handles = [\n    {\n      type: \"target\",\n      position: Position.Left,\n      id: `${id}-system`,\n      style: { top: `${100/3}%` }\n    },\n    {\n      type: \"target\",\n      position: Position.Left,\n      id: `${id}-prompt`,\n      style: { top: `${200/3}%` }\n    },\n    {\n      type: \"source\",\n      position: Position.Right,\n      id: `${id}-response`\n    }\n  ];\n\n  const fields = [\n    {\n      type: 'text',\n      content: 'This is a LLM.'\n    }\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"LLM\"\n      description=\"This is a LLM.\"\n      handles={handles}\n      fields={fields}\n    />\n  );\n};\n"], "mappings": ";AAAA;;AAEA,SAASA,QAAQ,QAAQ,WAAW;AACpC,SAASC,QAAQ,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,OAAO,MAAMC,OAAO,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EACvC,MAAMC,OAAO,GAAG,CACd;IACEC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAET,QAAQ,CAACU,IAAI;IACvBL,EAAE,EAAG,GAAEA,EAAG,SAAQ;IAClBM,KAAK,EAAE;MAAEC,GAAG,EAAG,GAAE,GAAG,GAAC,CAAE;IAAG;EAC5B,CAAC,EACD;IACEJ,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAET,QAAQ,CAACU,IAAI;IACvBL,EAAE,EAAG,GAAEA,EAAG,SAAQ;IAClBM,KAAK,EAAE;MAAEC,GAAG,EAAG,GAAE,GAAG,GAAC,CAAE;IAAG;EAC5B,CAAC,EACD;IACEJ,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAET,QAAQ,CAACa,KAAK;IACxBR,EAAE,EAAG,GAAEA,EAAG;EACZ,CAAC,CACF;EAED,MAAMS,MAAM,GAAG,CACb;IACEN,IAAI,EAAE,MAAM;IACZO,OAAO,EAAE;EACX,CAAC,CACF;EAED,oBACEZ,OAAA,CAACF,QAAQ;IACPI,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXU,KAAK,EAAC,KAAK;IACXC,WAAW,EAAC,gBAAgB;IAC5BV,OAAO,EAAEA,OAAQ;IACjBO,MAAM,EAAEA;EAAO;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChB,CAAC;AAEN,CAAC;AAACC,EAAA,GAtCWlB,OAAO;AAAA,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}